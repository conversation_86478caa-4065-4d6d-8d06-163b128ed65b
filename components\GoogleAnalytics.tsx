"use client";

import Script from "next/script";

const GoogleAnalytics = () => {
  const gid = process.env.GA_MEASUREMENT_ID;

  if (!gid) {
    return null;
  }

  return (
    <>
      <Script
        async src="https://www.googletagmanager.com/gtag/js?id=G-4BWKSP9R8B"
      />
      <Script id="google-analytics">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());

          gtag('config', 'G-4BWKSP9R8B');
        `}
      </Script>
    </>
  );
};

export default GoogleAnalytics;
